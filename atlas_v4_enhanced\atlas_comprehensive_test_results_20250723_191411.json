{"test_run_info": {"timestamp": "2025-07-23T19:14:11.918797", "total_time": 0.1556236743927002, "base_url": "http://localhost:8002"}, "overall_results": {"total_tests": 34, "passed": 26, "failed": 7, "errors": 0, "skipped": 1, "pass_rate": 76.47, "avg_response_time": 0.004}, "category_breakdown": {"Category A": {"total": 8, "passed": 6, "failed": 2, "errors": 0, "skipped": 0}, "Category B": {"total": 8, "passed": 7, "failed": 1, "errors": 0, "skipped": 0}, "Category C": {"total": 7, "passed": 5, "failed": 2, "errors": 0, "skipped": 0}, "Category D": {"total": 5, "passed": 3, "failed": 1, "errors": 0, "skipped": 1}, "Category E": {"total": 2, "passed": 2, "failed": 0, "errors": 0, "skipped": 0}, "Category F": {"total": 2, "passed": 2, "failed": 0, "errors": 0, "skipped": 0}, "Category G": {"total": 1, "passed": 0, "failed": 1, "errors": 0, "skipped": 0}, "Category H": {"total": 1, "passed": 1, "failed": 0, "errors": 0, "skipped": 0}}, "detailed_results": [{"category": "Category A", "test_name": "Conversational AI", "description": "What's the difference between a market order and a limit order?", "status": "PASS", "response_time": 0.0022754669189453125, "details": {"response_length": 522}, "error_message": null, "timestamp": "2025-07-23T19:14:11.765567"}, {"category": "Category A", "test_name": "Conversational AI", "description": "Explain put-call parity in simple terms.", "status": "PASS", "response_time": 0.002593994140625, "details": {"response_length": 154}, "error_message": null, "timestamp": "2025-07-23T19:14:11.768490"}, {"category": "Category A", "test_name": "6-Point Analysis", "description": "Analysis for TSLA", "status": "PASS", "response_time": 0.0014903545379638672, "details": {"has_six_points": true, "response_length": 899}, "error_message": null, "timestamp": "2025-07-23T19:14:11.770392"}, {"category": "Category A", "test_name": "Grok Integration", "description": "Normal Grok request", "status": "PASS", "response_time": 0.0016679763793945312, "details": {"grok_used": false}, "error_message": null, "timestamp": "2025-07-23T19:14:11.772196"}, {"category": "Category A", "test_name": "Grok Fallback", "description": "Fallback to OpenAI/static", "status": "PASS", "response_time": 0.0011484622955322266, "details": {"fallback_used": false}, "error_message": null, "timestamp": "2025-07-23T19:14:11.773644"}, {"category": "Category A", "test_name": "Context Awareness", "description": "Multi-turn conversation", "status": "FAIL", "response_time": 0.002398967742919922, "details": {"remembers_context": false}, "error_message": null, "timestamp": "2025-07-23T19:14:11.777248"}, {"category": "Category A", "test_name": "Adaptive Communication", "description": "<PERSON><PERSON><PERSON> vs Expert explanation", "status": "FAIL", "response_time": 0.002084016799926758, "details": {"beginner_length": 319, "expert_length": 319, "adapts_level": false}, "error_message": null, "timestamp": "2025-07-23T19:14:11.779499"}, {"category": "Category A", "test_name": "Emotional Intelligence", "description": "Emotion detection and response", "status": "PASS", "response_time": 0.000978231430053711, "details": {"shows_empathy": true, "emotion_detected": null}, "error_message": null, "timestamp": "2025-07-23T19:14:11.780644"}, {"category": "Category B", "test_name": "Lee Method Detection", "description": "MSFT daily scan", "status": "PASS", "response_time": 0.0012969970703125, "details": {"has_lee_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.782189"}, {"category": "Category B", "test_name": "TTM Squeeze Detection", "description": "NVDA 15min histogram", "status": "PASS", "response_time": 0.0012252330780029297, "details": {"has_ttm_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.783623"}, {"category": "Category B", "test_name": "Real-time Scanner", "description": "Top 5 Lee Method signals", "status": "PASS", "response_time": 0.0013103485107421875, "details": {"symbols_found": 9}, "error_message": null, "timestamp": "2025-07-23T19:14:11.785257"}, {"category": "Category B", "test_name": "Trading Plan Generation", "description": "6-Point plan for GOOGL", "status": "PASS", "response_time": 0.0012969970703125, "details": {"has_plan_elements": true, "numbered_points": 6}, "error_message": null, "timestamp": "2025-07-23T19:14:11.786728"}, {"category": "Category B", "test_name": "Options Trading", "description": "Iron Condor construction", "status": "PASS", "response_time": 0.0010273456573486328, "details": {"has_options_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.787927"}, {"category": "Category B", "test_name": "Portfolio Optimization", "description": "3-stock portfolio optimization", "status": "PASS", "response_time": 0.0008385181427001953, "details": {"has_portfolio_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.788873"}, {"category": "Category B", "test_name": "Risk Management", "description": "95% VaR calculation", "status": "PASS", "response_time": 0.0008540153503417969, "details": {"has_var_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.789823"}, {"category": "Category B", "test_name": "Market Regime", "description": "Current regime identification", "status": "FAIL", "response_time": 0.0009026527404785156, "details": {"has_regime_analysis": false}, "error_message": null, "timestamp": "2025-07-23T19:14:11.790826"}, {"category": "Category C", "test_name": "Real-time Quotes", "description": "AMZN current price", "status": "PASS", "response_time": 0.000835418701171875, "details": {"has_price": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.791900"}, {"category": "Category C", "test_name": "Data Fallback", "description": "TSLA fallback price", "status": "PASS", "response_time": 0.0008370876312255859, "details": {"has_fallback_price": true, "fallback_used": false}, "error_message": null, "timestamp": "2025-07-23T19:14:11.792977"}, {"category": "Category C", "test_name": "News Integration", "description": "AAPL news summary", "status": "PASS", "response_time": 0.0008606910705566406, "details": {"has_news_content": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.793938"}, {"category": "Category C", "test_name": "Sentiment Analysis", "description": "GME Twitter sentiment", "status": "PASS", "response_time": 0.00086212158203125, "details": {"has_sentiment_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.794942"}, {"category": "Category C", "test_name": "ML Predictions", "description": "MSFT 5-day LSTM forecast", "status": "PASS", "response_time": 0.000789642333984375, "details": {"has_ml_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.795886"}, {"category": "Category C", "test_name": "Alternative Data", "description": "Satellite oil-rig analysis", "status": "FAIL", "response_time": 0.0008358955383300781, "details": {"has_alt_data_analysis": false}, "error_message": null, "timestamp": "2025-07-23T19:14:11.796821"}, {"category": "Category C", "test_name": "Global Markets", "description": "7203.T TSE quote", "status": "FAIL", "response_time": 0.0007581710815429688, "details": {"has_global_analysis": false}, "error_message": null, "timestamp": "2025-07-23T19:14:11.797677"}, {"category": "Category D", "test_name": "WebSocket Alerts", "description": "TSLA scanner alerts", "status": "SKIP", "response_time": 0.0, "details": {"reason": "WebSocket testing requires specialized setup"}, "error_message": null, "timestamp": "2025-07-23T19:14:11.797820"}, {"category": "Category D", "test_name": "Scanner Performance", "description": "500 symbols < 5 seconds", "status": "PASS", "response_time": 0.10426807403564453, "details": {"scan_time": 0.102328, "meets_performance": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.902156"}, {"category": "Category D", "test_name": "Live P&L Monitoring", "description": "Real-time P&L updates", "status": "PASS", "response_time": 0.00125885009765625, "details": {"has_pnl_data": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.903771"}, {"category": "Category D", "test_name": "Progress Tracking", "description": "Scanner progress after 30 seconds", "status": "PASS", "response_time": 0.0010526180267333984, "details": {"has_progress_data": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.905017"}, {"category": "Category D", "test_name": "Conversation Monitoring", "description": "Off-topic refocus", "status": "FAIL", "response_time": 0.002669095993041992, "details": {"refocuses_on_trading": false}, "error_message": null, "timestamp": "2025-07-23T19:14:11.907940"}, {"category": "Category E", "test_name": "Morning Briefing", "description": "8:00 AM CT briefing", "status": "PASS", "response_time": 0.0011036396026611328, "details": {"has_briefing": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.909385"}, {"category": "Category E", "test_name": "Educational RAG", "description": "TTM Squeeze book retrieval", "status": "PASS", "response_time": 0.0009829998016357422, "details": {"has_content": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.910525"}, {"category": "Category F", "test_name": "Health Endpoint", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.001112222671508789, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-23T19:14:11.911982"}, {"category": "Category F", "test_name": "Trading API", "description": "GET /api/v1/trading/positions", "status": "PASS", "response_time": 0.0010113716125488281, "details": {"has_positions": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.913161"}, {"category": "Category G", "test_name": "Multi-level Caching", "description": "Quote caching verification", "status": "FAIL", "response_time": 0.0028162002563476562, "details": {"first_request_time": 0.0011107921600341797, "second_request_time": 0.0017054080963134766, "cache_working": false}, "error_message": null, "timestamp": "2025-07-23T19:14:11.916239"}, {"category": "Category H", "test_name": "Division by Zero Protection", "description": "Position sizing edge case", "status": "PASS", "response_time": 0.0013484954833984375, "details": {"handles_error": true}, "error_message": null, "timestamp": "2025-07-23T19:14:11.918291"}], "production_readiness": {"ready": false, "critical_failures": [], "recommendations": ["High failure rate detected. Review system architecture and API implementations.", "Core AI functionality issues detected. Verify Grok integration and fallback mechanisms."]}}