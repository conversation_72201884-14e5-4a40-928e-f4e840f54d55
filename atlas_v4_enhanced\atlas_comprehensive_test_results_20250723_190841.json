{"test_run_info": {"timestamp": "2025-07-23T19:08:41.547471", "total_time": 0.1670362949371338, "base_url": "http://localhost:8002"}, "overall_results": {"total_tests": 34, "passed": 21, "failed": 12, "errors": 0, "skipped": 1, "pass_rate": 61.76, "avg_response_time": 0.005}, "category_breakdown": {"Category A": {"total": 8, "passed": 6, "failed": 2, "errors": 0, "skipped": 0}, "Category B": {"total": 8, "passed": 4, "failed": 4, "errors": 0, "skipped": 0}, "Category C": {"total": 7, "passed": 2, "failed": 5, "errors": 0, "skipped": 0}, "Category D": {"total": 5, "passed": 4, "failed": 0, "errors": 0, "skipped": 1}, "Category E": {"total": 2, "passed": 2, "failed": 0, "errors": 0, "skipped": 0}, "Category F": {"total": 2, "passed": 2, "failed": 0, "errors": 0, "skipped": 0}, "Category G": {"total": 1, "passed": 0, "failed": 1, "errors": 0, "skipped": 0}, "Category H": {"total": 1, "passed": 1, "failed": 0, "errors": 0, "skipped": 0}}, "detailed_results": [{"category": "Category A", "test_name": "Conversational AI", "description": "What's the difference between a market order and a limit order?", "status": "PASS", "response_time": 0.0012164115905761719, "details": {"response_length": 522}, "error_message": null, "timestamp": "2025-07-23T19:08:41.381746"}, {"category": "Category A", "test_name": "Conversational AI", "description": "Explain put-call parity in simple terms.", "status": "PASS", "response_time": 0.0011005401611328125, "details": {"response_length": 720}, "error_message": null, "timestamp": "2025-07-23T19:08:41.382963"}, {"category": "Category A", "test_name": "6-Point Analysis", "description": "Analysis for TSLA", "status": "PASS", "response_time": 0.0009527206420898438, "details": {"has_six_points": true, "response_length": 899}, "error_message": null, "timestamp": "2025-07-23T19:08:41.384040"}, {"category": "Category A", "test_name": "Grok Integration", "description": "Normal Grok request", "status": "PASS", "response_time": 0.0013821125030517578, "details": {"grok_used": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.385530"}, {"category": "Category A", "test_name": "Grok Fallback", "description": "Fallback to OpenAI/static", "status": "PASS", "response_time": 0.0013480186462402344, "details": {"fallback_used": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.387016"}, {"category": "Category A", "test_name": "Context Awareness", "description": "Multi-turn conversation", "status": "FAIL", "response_time": 0.005080223083496094, "details": {"remembers_context": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.392331"}, {"category": "Category A", "test_name": "Adaptive Communication", "description": "<PERSON><PERSON><PERSON> vs Expert explanation", "status": "PASS", "response_time": 0.0051534175872802734, "details": {"beginner_length": 537, "expert_length": 544, "adapts_level": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.397866"}, {"category": "Category A", "test_name": "Emotional Intelligence", "description": "Emotion detection and response", "status": "FAIL", "response_time": 0.001968860626220703, "details": {"shows_empathy": false, "emotion_detected": null}, "error_message": null, "timestamp": "2025-07-23T19:08:41.400186"}, {"category": "Category B", "test_name": "Lee Method Detection", "description": "MSFT daily scan", "status": "FAIL", "response_time": 0.0017697811126708984, "details": {"has_lee_analysis": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.402200"}, {"category": "Category B", "test_name": "TTM Squeeze Detection", "description": "NVDA 15min histogram", "status": "FAIL", "response_time": 0.0017237663269042969, "details": {"has_ttm_analysis": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.404113"}, {"category": "Category B", "test_name": "Real-time Scanner", "description": "Top 5 Lee Method signals", "status": "PASS", "response_time": 0.0018951892852783203, "details": {"symbols_found": 5}, "error_message": null, "timestamp": "2025-07-23T19:08:41.406716"}, {"category": "Category B", "test_name": "Trading Plan Generation", "description": "6-Point plan for GOOGL", "status": "PASS", "response_time": 0.0018634796142578125, "details": {"has_plan_elements": true, "numbered_points": 6}, "error_message": null, "timestamp": "2025-07-23T19:08:41.408835"}, {"category": "Category B", "test_name": "Options Trading", "description": "Iron Condor construction", "status": "FAIL", "response_time": 0.0020508766174316406, "details": {"has_options_analysis": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.411214"}, {"category": "Category B", "test_name": "Portfolio Optimization", "description": "3-stock portfolio optimization", "status": "FAIL", "response_time": 0.002048015594482422, "details": {"has_portfolio_analysis": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.413510"}, {"category": "Category B", "test_name": "Risk Management", "description": "95% VaR calculation", "status": "PASS", "response_time": 0.0018537044525146484, "details": {"has_var_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.415612"}, {"category": "Category B", "test_name": "Market Regime", "description": "Current regime identification", "status": "PASS", "response_time": 0.0015125274658203125, "details": {"has_regime_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.417376"}, {"category": "Category C", "test_name": "Real-time Quotes", "description": "AMZN current price", "status": "FAIL", "response_time": 0.0013115406036376953, "details": {"has_price": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.419175"}, {"category": "Category C", "test_name": "Data Fallback", "description": "TSLA fallback price", "status": "FAIL", "response_time": 0.0015816688537597656, "details": {"has_fallback_price": false, "fallback_used": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.420913"}, {"category": "Category C", "test_name": "News Integration", "description": "AAPL news summary", "status": "FAIL", "response_time": 0.0013568401336669922, "details": {"has_news_content": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.422435"}, {"category": "Category C", "test_name": "Sentiment Analysis", "description": "GME Twitter sentiment", "status": "FAIL", "response_time": 0.001352548599243164, "details": {"has_sentiment_analysis": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.423999"}, {"category": "Category C", "test_name": "ML Predictions", "description": "MSFT 5-day LSTM forecast", "status": "FAIL", "response_time": 0.0012288093566894531, "details": {"has_ml_analysis": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.425375"}, {"category": "Category C", "test_name": "Alternative Data", "description": "Satellite oil-rig analysis", "status": "PASS", "response_time": 0.0014469623565673828, "details": {"has_alt_data_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.426995"}, {"category": "Category C", "test_name": "Global Markets", "description": "7203.T TSE quote", "status": "PASS", "response_time": 0.0013473033905029297, "details": {"has_global_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.428533"}, {"category": "Category D", "test_name": "WebSocket Alerts", "description": "TSLA scanner alerts", "status": "SKIP", "response_time": 0.0, "details": {"reason": "WebSocket testing requires specialized setup"}, "error_message": null, "timestamp": "2025-07-23T19:08:41.428797"}, {"category": "Category D", "test_name": "Scanner Performance", "description": "500 symbols < 5 seconds", "status": "PASS", "response_time": 0.10667943954467773, "details": {"scan_time": 0.105081, "meets_performance": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.535595"}, {"category": "Category D", "test_name": "Live P&L Monitoring", "description": "Real-time P&L updates", "status": "PASS", "response_time": 0.001558542251586914, "details": {"has_pnl_data": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.537317"}, {"category": "Category D", "test_name": "Progress Tracking", "description": "Scanner progress after 30 seconds", "status": "PASS", "response_time": 0.0010194778442382812, "details": {"has_progress_data": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.538469"}, {"category": "Category D", "test_name": "Conversation Monitoring", "description": "Off-topic refocus", "status": "PASS", "response_time": 0.0010273456573486328, "details": {"refocuses_on_trading": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.539637"}, {"category": "Category E", "test_name": "Morning Briefing", "description": "8:00 AM CT briefing", "status": "PASS", "response_time": 0.0009222030639648438, "details": {"has_briefing": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.540748"}, {"category": "Category E", "test_name": "Educational RAG", "description": "TTM Squeeze book retrieval", "status": "PASS", "response_time": 0.0010752677917480469, "details": {"has_content": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.541983"}, {"category": "Category F", "test_name": "Health Endpoint", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.0008921623229980469, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-23T19:08:41.543093"}, {"category": "Category F", "test_name": "Trading API", "description": "GET /api/v1/trading/positions", "status": "PASS", "response_time": 0.0008661746978759766, "details": {"has_positions": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.544062"}, {"category": "Category G", "test_name": "Multi-level Caching", "description": "Quote caching verification", "status": "FAIL", "response_time": 0.0017690658569335938, "details": {"first_request_time": 0.0008988380432128906, "second_request_time": 0.0008702278137207031, "cache_working": false}, "error_message": null, "timestamp": "2025-07-23T19:08:41.546027"}, {"category": "Category H", "test_name": "Division by Zero Protection", "description": "Position sizing edge case", "status": "PASS", "response_time": 0.0010573863983154297, "details": {"handles_error": true}, "error_message": null, "timestamp": "2025-07-23T19:08:41.547322"}], "production_readiness": {"ready": false, "critical_failures": [], "recommendations": ["High failure rate detected. Review system architecture and API implementations.", "Core AI functionality issues detected. Verify Grok integration and fallback mechanisms."]}}