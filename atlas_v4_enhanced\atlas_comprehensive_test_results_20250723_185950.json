{"test_run_info": {"timestamp": "2025-07-23T18:59:50.592622", "total_time": 0.044936418533325195, "base_url": "http://localhost:8002"}, "overall_results": {"total_tests": 34, "passed": 18, "failed": 15, "errors": 0, "skipped": 1, "pass_rate": 52.94, "avg_response_time": 0.001}, "category_breakdown": {"Category A": {"total": 8, "passed": 6, "failed": 2, "errors": 0, "skipped": 0}, "Category B": {"total": 8, "passed": 4, "failed": 4, "errors": 0, "skipped": 0}, "Category C": {"total": 7, "passed": 5, "failed": 2, "errors": 0, "skipped": 0}, "Category D": {"total": 5, "passed": 0, "failed": 4, "errors": 0, "skipped": 1}, "Category E": {"total": 2, "passed": 2, "failed": 0, "errors": 0, "skipped": 0}, "Category F": {"total": 2, "passed": 1, "failed": 1, "errors": 0, "skipped": 0}, "Category G": {"total": 1, "passed": 0, "failed": 1, "errors": 0, "skipped": 0}, "Category H": {"total": 1, "passed": 0, "failed": 1, "errors": 0, "skipped": 0}}, "detailed_results": [{"category": "Category A", "test_name": "Conversational AI", "description": "What's the difference between a market order and a limit order?", "status": "PASS", "response_time": 0.0017740726470947266, "details": {"response_length": 185}, "error_message": null, "timestamp": "2025-07-23T18:59:50.549533"}, {"category": "Category A", "test_name": "Conversational AI", "description": "Explain put-call parity in simple terms.", "status": "PASS", "response_time": 0.000934600830078125, "details": {"response_length": 162}, "error_message": null, "timestamp": "2025-07-23T18:59:50.550556"}, {"category": "Category A", "test_name": "6-Point Analysis", "description": "Analysis for TSLA", "status": "FAIL", "response_time": 0.0007736682891845703, "details": {"has_six_points": false, "response_length": 164}, "error_message": null, "timestamp": "2025-07-23T18:59:50.551414"}, {"category": "Category A", "test_name": "Grok Integration", "description": "Normal Grok request", "status": "PASS", "response_time": 0.0009047985076904297, "details": {"grok_used": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.553332"}, {"category": "Category A", "test_name": "Grok Fallback", "description": "Fallback to OpenAI/static", "status": "PASS", "response_time": 0.0008478164672851562, "details": {"fallback_used": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.554261"}, {"category": "Category A", "test_name": "Context Awareness", "description": "Multi-turn conversation", "status": "FAIL", "response_time": 0.0016009807586669922, "details": {"remembers_context": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.555944"}, {"category": "Category A", "test_name": "Adaptive Communication", "description": "<PERSON><PERSON><PERSON> vs Expert explanation", "status": "PASS", "response_time": 0.0015442371368408203, "details": {"beginner_length": 165, "expert_length": 155, "adapts_level": true}, "error_message": null, "timestamp": "2025-07-23T18:59:50.557574"}, {"category": "Category A", "test_name": "Emotional Intelligence", "description": "Emotion detection and response", "status": "PASS", "response_time": 0.0022411346435546875, "details": {"shows_empathy": true, "emotion_detected": null}, "error_message": null, "timestamp": "2025-07-23T18:59:50.559905"}, {"category": "Category B", "test_name": "Lee Method Detection", "description": "MSFT daily scan", "status": "PASS", "response_time": 0.0025429725646972656, "details": {"has_lee_analysis": true}, "error_message": null, "timestamp": "2025-07-23T18:59:50.562791"}, {"category": "Category B", "test_name": "TTM Squeeze Detection", "description": "NVDA 15min histogram", "status": "PASS", "response_time": 0.0014286041259765625, "details": {"has_ttm_analysis": true}, "error_message": null, "timestamp": "2025-07-23T18:59:50.565432"}, {"category": "Category B", "test_name": "Real-time Scanner", "description": "Top 5 Lee Method signals", "status": "PASS", "response_time": 0.001130819320678711, "details": {"symbols_found": 3}, "error_message": null, "timestamp": "2025-07-23T18:59:50.566783"}, {"category": "Category B", "test_name": "Trading Plan Generation", "description": "6-Point plan for GOOGL", "status": "FAIL", "response_time": 0.0009381771087646484, "details": {"has_plan_elements": false, "numbered_points": 0}, "error_message": null, "timestamp": "2025-07-23T18:59:50.567902"}, {"category": "Category B", "test_name": "Options Trading", "description": "Iron Condor construction", "status": "FAIL", "response_time": 0.0008487701416015625, "details": {"has_options_analysis": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.568900"}, {"category": "Category B", "test_name": "Portfolio Optimization", "description": "3-stock portfolio optimization", "status": "FAIL", "response_time": 0.0007867813110351562, "details": {"has_portfolio_analysis": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.569791"}, {"category": "Category B", "test_name": "Risk Management", "description": "95% VaR calculation", "status": "FAIL", "response_time": 0.0007927417755126953, "details": {"has_var_analysis": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.570681"}, {"category": "Category B", "test_name": "Market Regime", "description": "Current regime identification", "status": "PASS", "response_time": 0.0008096694946289062, "details": {"has_regime_analysis": true}, "error_message": null, "timestamp": "2025-07-23T18:59:50.571589"}, {"category": "Category C", "test_name": "Real-time Quotes", "description": "AMZN current price", "status": "PASS", "response_time": 0.000881195068359375, "details": {"has_price": true}, "error_message": null, "timestamp": "2025-07-23T18:59:50.572709"}, {"category": "Category C", "test_name": "Data Fallback", "description": "TSLA fallback price", "status": "PASS", "response_time": 0.0008542537689208984, "details": {"has_fallback_price": true, "fallback_used": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.573670"}, {"category": "Category C", "test_name": "News Integration", "description": "AAPL news summary", "status": "FAIL", "response_time": 0.0008065700531005859, "details": {"has_news_content": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.574569"}, {"category": "Category C", "test_name": "Sentiment Analysis", "description": "GME Twitter sentiment", "status": "FAIL", "response_time": 0.0007619857788085938, "details": {"has_sentiment_analysis": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.575424"}, {"category": "Category C", "test_name": "ML Predictions", "description": "MSFT 5-day LSTM forecast", "status": "PASS", "response_time": 0.0007536411285400391, "details": {"has_ml_analysis": true}, "error_message": null, "timestamp": "2025-07-23T18:59:50.576290"}, {"category": "Category C", "test_name": "Alternative Data", "description": "Satellite oil-rig analysis", "status": "PASS", "response_time": 0.0010724067687988281, "details": {"has_alt_data_analysis": true}, "error_message": null, "timestamp": "2025-07-23T18:59:50.577461"}, {"category": "Category C", "test_name": "Global Markets", "description": "7203.T TSE quote", "status": "PASS", "response_time": 0.0012543201446533203, "details": {"has_global_analysis": true}, "error_message": null, "timestamp": "2025-07-23T18:59:50.578932"}, {"category": "Category D", "test_name": "WebSocket Alerts", "description": "TSLA scanner alerts", "status": "SKIP", "response_time": 0.0, "details": {"reason": "WebSocket testing requires specialized setup"}, "error_message": null, "timestamp": "2025-07-23T18:59:50.579082"}, {"category": "Category D", "test_name": "Scanner Performance", "description": "500 symbols < 5 seconds", "status": "FAIL", "response_time": 0.0010600090026855469, "details": {"status_code": null}, "error_message": null, "timestamp": "2025-07-23T18:59:50.580225"}, {"category": "Category D", "test_name": "Live P&L Monitoring", "description": "Real-time P&L updates", "status": "FAIL", "response_time": 0.000986337661743164, "details": {"status_code": null}, "error_message": null, "timestamp": "2025-07-23T18:59:50.581309"}, {"category": "Category D", "test_name": "Progress Tracking", "description": "Scanner progress after 30 seconds", "status": "FAIL", "response_time": 0.0009522438049316406, "details": {"status_code": null}, "error_message": null, "timestamp": "2025-07-23T18:59:50.582428"}, {"category": "Category D", "test_name": "Conversation Monitoring", "description": "Off-topic refocus", "status": "FAIL", "response_time": 0.001409769058227539, "details": {"refocuses_on_trading": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.584081"}, {"category": "Category E", "test_name": "Morning Briefing", "description": "8:00 AM CT briefing", "status": "PASS", "response_time": 0.001199960708618164, "details": {"has_briefing": true}, "error_message": null, "timestamp": "2025-07-23T18:59:50.586001"}, {"category": "Category E", "test_name": "Educational RAG", "description": "TTM Squeeze book retrieval", "status": "PASS", "response_time": 0.0010514259338378906, "details": {"has_content": true}, "error_message": null, "timestamp": "2025-07-23T18:59:50.587201"}, {"category": "Category F", "test_name": "Health Endpoint", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.0010671615600585938, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-23T18:59:50.588464"}, {"category": "Category F", "test_name": "Trading API", "description": "GET /api/v1/trading/positions", "status": "FAIL", "response_time": 0.0009021759033203125, "details": {"has_positions": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.589462"}, {"category": "Category G", "test_name": "Multi-level Caching", "description": "Quote caching verification", "status": "FAIL", "response_time": 0.001802682876586914, "details": {"first_request_time": 0.000919342041015625, "second_request_time": 0.0008833408355712891, "cache_working": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.591459"}, {"category": "Category H", "test_name": "Division by Zero Protection", "description": "Position sizing edge case", "status": "FAIL", "response_time": 0.0008482933044433594, "details": {"handles_error": false}, "error_message": null, "timestamp": "2025-07-23T18:59:50.592510"}], "production_readiness": {"ready": false, "critical_failures": [], "recommendations": ["High failure rate detected. Review system architecture and API implementations.", "Core AI functionality issues detected. Verify Grok integration and fallback mechanisms.", "API endpoint issues detected. Check server configuration and endpoint implementations.", "Critical vulnerability safeguards failing. Immediate attention required for production safety."]}}