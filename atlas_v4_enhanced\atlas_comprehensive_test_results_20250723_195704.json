{"test_run_info": {"timestamp": "2025-07-23T19:57:04.481335", "total_time": 0.31773805618286133, "base_url": "http://localhost:8002"}, "overall_results": {"total_tests": 34, "passed": 27, "failed": 6, "errors": 0, "skipped": 1, "pass_rate": 79.41, "avg_response_time": 0.009}, "category_breakdown": {"Category A": {"total": 8, "passed": 7, "failed": 1, "errors": 0, "skipped": 0}, "Category B": {"total": 8, "passed": 7, "failed": 1, "errors": 0, "skipped": 0}, "Category C": {"total": 7, "passed": 5, "failed": 2, "errors": 0, "skipped": 0}, "Category D": {"total": 5, "passed": 3, "failed": 1, "errors": 0, "skipped": 1}, "Category E": {"total": 2, "passed": 2, "failed": 0, "errors": 0, "skipped": 0}, "Category F": {"total": 2, "passed": 2, "failed": 0, "errors": 0, "skipped": 0}, "Category G": {"total": 1, "passed": 0, "failed": 1, "errors": 0, "skipped": 0}, "Category H": {"total": 1, "passed": 1, "failed": 0, "errors": 0, "skipped": 0}}, "detailed_results": [{"category": "Category A", "test_name": "Conversational AI", "description": "What's the difference between a market order and a limit order?", "status": "PASS", "response_time": 0.0011754035949707031, "details": {"response_length": 522}, "error_message": null, "timestamp": "2025-07-23T19:57:04.164896"}, {"category": "Category A", "test_name": "Conversational AI", "description": "Explain put-call parity in simple terms.", "status": "PASS", "response_time": 0.0008535385131835938, "details": {"response_length": 154}, "error_message": null, "timestamp": "2025-07-23T19:57:04.165848"}, {"category": "Category A", "test_name": "6-Point Analysis", "description": "Analysis for TSLA", "status": "PASS", "response_time": 0.0008714199066162109, "details": {"has_six_points": true, "response_length": 899}, "error_message": null, "timestamp": "2025-07-23T19:57:04.166826"}, {"category": "Category A", "test_name": "Grok Integration", "description": "Normal Grok request", "status": "PASS", "response_time": 0.0016183853149414062, "details": {"grok_used": false}, "error_message": null, "timestamp": "2025-07-23T19:57:04.168552"}, {"category": "Category A", "test_name": "Grok Fallback", "description": "Fallback to OpenAI/static", "status": "PASS", "response_time": 0.0008623600006103516, "details": {"fallback_used": false}, "error_message": null, "timestamp": "2025-07-23T19:57:04.169654"}, {"category": "Category A", "test_name": "Context Awareness", "description": "Multi-turn conversation", "status": "FAIL", "response_time": 0.0016856193542480469, "details": {"remembers_context": false}, "error_message": null, "timestamp": "2025-07-23T19:57:04.171457"}, {"category": "Category A", "test_name": "Adaptive Communication", "description": "<PERSON><PERSON><PERSON> vs Expert explanation", "status": "PASS", "response_time": 0.0019516944885253906, "details": {"beginner_length": 319, "expert_length": 311, "adapts_level": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.173506"}, {"category": "Category A", "test_name": "Emotional Intelligence", "description": "Emotion detection and response", "status": "PASS", "response_time": 0.0008852481842041016, "details": {"shows_empathy": true, "emotion_detected": null}, "error_message": null, "timestamp": "2025-07-23T19:57:04.175676"}, {"category": "Category B", "test_name": "Lee Method Detection", "description": "MSFT daily scan", "status": "PASS", "response_time": 0.0022194385528564453, "details": {"has_lee_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.178657"}, {"category": "Category B", "test_name": "TTM Squeeze Detection", "description": "NVDA 15min histogram", "status": "PASS", "response_time": 0.0030126571655273438, "details": {"has_ttm_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.181927"}, {"category": "Category B", "test_name": "Real-time Scanner", "description": "Top 5 Lee Method signals", "status": "PASS", "response_time": 0.0010292530059814453, "details": {"symbols_found": 9}, "error_message": null, "timestamp": "2025-07-23T19:57:04.183298"}, {"category": "Category B", "test_name": "Trading Plan Generation", "description": "6-Point plan for GOOGL", "status": "PASS", "response_time": 0.000995635986328125, "details": {"has_plan_elements": true, "numbered_points": 6}, "error_message": null, "timestamp": "2025-07-23T19:57:04.184588"}, {"category": "Category B", "test_name": "Options Trading", "description": "Iron Condor construction", "status": "PASS", "response_time": 0.0008373260498046875, "details": {"has_options_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.185525"}, {"category": "Category B", "test_name": "Portfolio Optimization", "description": "3-stock portfolio optimization", "status": "PASS", "response_time": 0.0007824897766113281, "details": {"has_portfolio_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.186400"}, {"category": "Category B", "test_name": "Risk Management", "description": "95% VaR calculation", "status": "PASS", "response_time": 0.0008158683776855469, "details": {"has_var_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.187316"}, {"category": "Category B", "test_name": "Market Regime", "description": "Current regime identification", "status": "FAIL", "response_time": 0.0009136199951171875, "details": {"has_regime_analysis": false}, "error_message": null, "timestamp": "2025-07-23T19:57:04.188338"}, {"category": "Category C", "test_name": "Real-time Quotes", "description": "AMZN current price", "status": "PASS", "response_time": 0.0009870529174804688, "details": {"has_price": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.189858"}, {"category": "Category C", "test_name": "Data Fallback", "description": "TSLA fallback price", "status": "PASS", "response_time": 0.0009016990661621094, "details": {"has_fallback_price": true, "fallback_used": false}, "error_message": null, "timestamp": "2025-07-23T19:57:04.190915"}, {"category": "Category C", "test_name": "News Integration", "description": "AAPL news summary", "status": "PASS", "response_time": 0.001146554946899414, "details": {"has_news_content": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.192179"}, {"category": "Category C", "test_name": "Sentiment Analysis", "description": "GME Twitter sentiment", "status": "PASS", "response_time": 0.0012538433074951172, "details": {"has_sentiment_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.193989"}, {"category": "Category C", "test_name": "ML Predictions", "description": "MSFT 5-day LSTM forecast", "status": "PASS", "response_time": 0.15459275245666504, "details": {"has_ml_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.348718"}, {"category": "Category C", "test_name": "Alternative Data", "description": "Satellite oil-rig analysis", "status": "FAIL", "response_time": 0.0011472702026367188, "details": {"has_alt_data_analysis": false}, "error_message": null, "timestamp": "2025-07-23T19:57:04.350044"}, {"category": "Category C", "test_name": "Global Markets", "description": "7203.T TSE quote", "status": "FAIL", "response_time": 0.0009009838104248047, "details": {"has_global_analysis": false}, "error_message": null, "timestamp": "2025-07-23T19:57:04.351249"}, {"category": "Category D", "test_name": "WebSocket Alerts", "description": "TSLA scanner alerts", "status": "SKIP", "response_time": 0.0, "details": {"reason": "WebSocket testing requires specialized setup"}, "error_message": null, "timestamp": "2025-07-23T19:57:04.351490"}, {"category": "Category D", "test_name": "Scanner Performance", "description": "500 symbols < 5 seconds", "status": "PASS", "response_time": 0.11100935935974121, "details": {"scan_time": 0.104024, "meets_performance": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.462594"}, {"category": "Category D", "test_name": "Live P&L Monitoring", "description": "Real-time P&L updates", "status": "PASS", "response_time": 0.0012938976287841797, "details": {"has_pnl_data": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.464248"}, {"category": "Category D", "test_name": "Progress Tracking", "description": "Scanner progress after 30 seconds", "status": "PASS", "response_time": 0.0009603500366210938, "details": {"has_progress_data": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.465412"}, {"category": "Category D", "test_name": "Conversation Monitoring", "description": "Off-topic refocus", "status": "FAIL", "response_time": 0.0009887218475341797, "details": {"refocuses_on_trading": false}, "error_message": null, "timestamp": "2025-07-23T19:57:04.466641"}, {"category": "Category E", "test_name": "Morning Briefing", "description": "8:00 AM CT briefing", "status": "PASS", "response_time": 0.002565622329711914, "details": {"has_briefing": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.469481"}, {"category": "Category E", "test_name": "Educational RAG", "description": "TTM Squeeze book retrieval", "status": "PASS", "response_time": 0.0009984970092773438, "details": {"has_content": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.470665"}, {"category": "Category F", "test_name": "Health Endpoint", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.0010390281677246094, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-23T19:57:04.471977"}, {"category": "Category F", "test_name": "Trading API", "description": "GET /api/v1/trading/positions", "status": "PASS", "response_time": 0.0010497570037841797, "details": {"has_positions": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.473192"}, {"category": "Category G", "test_name": "Multi-level Caching", "description": "Quote caching verification", "status": "FAIL", "response_time": 0.0059926509857177734, "details": {"first_request_time": 0.0010666847229003906, "second_request_time": 0.004925966262817383, "cache_working": false}, "error_message": null, "timestamp": "2025-07-23T19:57:04.479430"}, {"category": "Category H", "test_name": "Division by Zero Protection", "description": "Position sizing edge case", "status": "PASS", "response_time": 0.0013451576232910156, "details": {"handles_error": true}, "error_message": null, "timestamp": "2025-07-23T19:57:04.481172"}], "production_readiness": {"ready": false, "critical_failures": [], "recommendations": ["Core AI functionality issues detected. Verify Grok integration and fallback mechanisms."]}}