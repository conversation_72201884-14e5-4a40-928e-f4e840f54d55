{"timestamp": "2025-07-23T19:09:13.964648", "total_tests": 34, "successful": 34, "failed": 0, "errors": 0, "success_rate": 100.0, "results": [{"category": "Category A - Core AI & Conversational Features", "question": "What's the difference between a market order and a limit order?", "result": {"status": "SUCCESS", "response": "\n**Market Order vs Limit Order**\n\n**Market Order**:\n- Executes immediately at current market price\n- Guarantees execution but not price\n- Use when you want immediate entry/exit\n- Risk: Slippage in volatile markets\n\n**Limit Order**:\n- Executes only at specified price or better\n- Guarantees price but not execution\n- Use when you want price control\n- Risk: May not fill if price doesn't reach limit\n\n**Best Practice**: Use limit orders in volatile markets, market orders for liquid stocks during regular hours.\n            ", "response_time": 2.0161328315734863, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "Explain put‑call parity in simple terms.", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"Explain put‑call parity in simple terms.\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.0014786720275878906, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "Give me a 6‑Point analysis for TSLA today.", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for GIVE. Market data provider may be unavailable.", "response_time": 0.0015380382537841797, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "What's your market view on AAPL?", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for WHAT. Market data provider may be unavailable.", "response_time": 0.0013813972473144531, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "Tell me about AAPL trend", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for TELL. Market data provider may be unavailable.", "response_time": 0.0013070106506347656, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "What if I wanted a tighter stop?", "result": {"status": "SUCCESS", "response": "\nBased on our previous discussion about TREND, here are tighter stop-loss strategies:\n\n**Tighter Stop Options**:\n1. **ATR-based**: Use 1x ATR instead of 2x ATR for closer stops\n2. **Support-based**: Place stop just below nearest support level\n3. **Percentage-based**: Use 1-2% stop instead of 3-5%\n4. **Time-based**: Exit if no movement within 2-3 days\n\n**Trade-offs with Tighter Stops**:\n✅ Reduced risk per trade\n✅ Better risk/reward ratios\n❌ Higher probability of being stopped out\n❌ May miss larger moves\n\nFor TREND, consider the current volatility and recent trading range when setting your stop level.\n                ", "response_time": 0.001271963119506836, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "Give me a beginner‑level explanation of RSI", "result": {"status": "SUCCESS", "response": "\n**RSI (Relative Strength Index)**\n\n**What it measures**: Price momentum on a scale of 0-100\n\n**Key Levels**:\n- Above 70: Potentially overbought (sell signal)\n- Below 30: Potentially oversold (buy signal)\n- 50: Neutral momentum\n\n**How to use**:\n1. Look for divergences (price vs RSI direction)\n2. Wait for RSI to cross back from extreme levels\n3. Combine with other indicators for confirmation\n\n**Pro tip**: RSI can stay overbought/oversold longer than expected in strong trends. Use it as a warning, not an absolute signal.\n            ", "response_time": 0.0017774105072021484, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "Now explain it like I'm an expert", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"Now explain it like I'm an expert\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.001422882080078125, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "I'm freaked out by volatility. How do I manage risk?", "result": {"status": "SUCCESS", "response": "\nBased on our previous discussion about LEVEL, here are tighter stop-loss strategies:\n\n**Tighter Stop Options**:\n1. **ATR-based**: Use 1x ATR instead of 2x ATR for closer stops\n2. **Support-based**: Place stop just below nearest support level\n3. **Percentage-based**: Use 1-2% stop instead of 3-5%\n4. **Time-based**: Exit if no movement within 2-3 days\n\n**Trade-offs with Tighter Stops**:\n✅ Reduced risk per trade\n✅ Better risk/reward ratios\n❌ Higher probability of being stopped out\n❌ May miss larger moves\n\nFor LEVEL, consider the current volatility and recent trading range when setting your stop level.\n                ", "response_time": 0.0014011859893798828, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "Scan MSFT on daily for the Lee Method signal—what do you see?", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for SCAN. Market data provider may be unavailable.", "response_time": 0.0013697147369384766, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "What does the current TTM Squeeze histogram show for NVDA at 15 min?", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for WHAT. Market data provider may be unavailable.", "response_time": 0.0016198158264160156, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "List the top 5 S&P 500 stocks with Lee Method buy signals right now.", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"List the top 5 S&P 500 stocks with Lee Method buy signals right now.\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.001445770263671875, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "Provide a 6‑Point trade plan for GOOGL at market open.", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for POINT. Market data provider may be unavailable.", "response_time": 0.001361846923828125, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "Construct an Iron Condor on SPY expiring in 30 days targeting $500 profit, moderate risk.", "result": {"status": "SUCCESS", "response": "\nBased on our previous discussion about SPY, here are tighter stop-loss strategies:\n\n**Tighter Stop Options**:\n1. **ATR-based**: Use 1x ATR instead of 2x ATR for closer stops\n2. **Support-based**: Place stop just below nearest support level\n3. **Percentage-based**: Use 1-2% stop instead of 3-5%\n4. **Time-based**: Exit if no movement within 2-3 days\n\n**Trade-offs with Tighter Stops**:\n✅ Reduced risk per trade\n✅ Better risk/reward ratios\n❌ Higher probability of being stopped out\n❌ May miss larger moves\n\nFor SPY, consider the current volatility and recent trading range when setting your stop level.\n                ", "response_time": 0.001314401626586914, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "Optimize a portfolio of AAPL, TSLA, NVDA for 10% annualized return.", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for OF. Market data provider may be unavailable.", "response_time": 0.0019185543060302734, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "Calculate my portfolio's 95% VaR.", "result": {"status": "SUCCESS", "response": "\n**Portfolio Value at Risk (95% Confidence)**\n\nPortfolio Value: $100,000\nDaily VaR: $3,290.0 (3.29%)\n\nThere is a 95.0% chance that daily losses will not exceed $3,290.00\n\n**Risk Metrics**:\n- Confidence Level: 95.0%\n- Daily Volatility: 2.0%\n- Time Horizon: 1 day\n\nThis means there's only a 5% chance your portfolio will lose more than $3,290.0 in a single day.\n            ", "response_time": 0.0014433860778808594, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "Which market regime are we in—bull, bear, or sideways?", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"Which market regime are we in—bull, bear, or sideways?\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.0013964176177978516, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "What's AMZN trading at right now?", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for WHAT. Market data provider may be unavailable.", "response_time": 0.0012509822845458984, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "What's TSLA's last price?", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for WHAT. Market data provider may be unavailable.", "response_time": 0.0017642974853515625, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "Summarize the latest headlines impacting AAPL.", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for THE. Market data provider may be unavailable.", "response_time": 0.0012955665588378906, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "What's the Twitter sentiment on GME today?", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"What's the Twitter sentiment on GME today?\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.0018582344055175781, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "Forecast MSFT's next 5 days using LSTM.", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for MSFT. Market data provider may be unavailable.", "response_time": 0.0017817020416259766, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "Use satellite oil‐rig data to gauge energy sector outlook.", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"Use satellite oil‐rig data to gauge energy sector outlook.\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.0014066696166992188, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "Get live quotes for 7203.T (Toyota) on TSE.", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"Get live quotes for 7203.T (Toyota) on TSE.\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.0016999244689941406, "status_code": 200}}, {"category": "Category D - Real-time Capabilities", "question": "Subscribe to TSLA scanner alerts—did you get a sub‑second notification?", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for TO. Market data provider may be unavailable.", "response_time": 0.0013127326965332031, "status_code": 200}}, {"category": "Category D - Real-time Capabilities", "question": "Scan 500 symbols and verify results return within 5 seconds.", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"Scan 500 symbols and verify results return within 5 seconds.\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.0013687610626220703, "status_code": 200}}, {"category": "Category D - Real-time Capabilities", "question": "Show real‑time P&L updates for my open positions.", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"Show real‑time P&L updates for my open positions.\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.0017223358154296875, "status_code": 200}}, {"category": "Category D - Real-time Capabilities", "question": "What's the scanner progress after 30 seconds?", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"What's the scanner progress after 30 seconds?\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.001302957534790039, "status_code": 200}}, {"category": "Category D - Real-time Capabilities", "question": "What's the weather?", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"What's the weather?\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.001975536346435547, "status_code": 200}}, {"category": "Category E - Advanced Features", "question": "Send me the 8:00 AM CT market briefing.", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"Send me the 8:00 AM CT market briefing.\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.0012660026550292969, "status_code": 200}}, {"category": "Category E - Advanced Features", "question": "Retrieve Chapter 3 summary from the TTM Squeeze book.", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"Retrieve Chapter 3 summary from the TTM Squeeze book.\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.0017015933990478516, "status_code": 200}}, {"category": "Category E - Advanced Features", "question": "Place a paper trade: buy 100 AAPL at market.", "result": {"status": "SUCCESS", "response": "Unable to fetch real-time quote for PLACE. Market data provider may be unavailable.", "response_time": 0.0014183521270751953, "status_code": 200}}, {"category": "Category E - Advanced Features", "question": "Report current CPU, memory, and API‐latency metrics.", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"Report current CPU, memory, and API‐latency metrics.\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.0012912750244140625, "status_code": 200}}, {"category": "Category E - Advanced Features", "question": "Check connectivity to all six specialized databases.", "result": {"status": "SUCCESS", "response": "\nI understand you're asking about: \"Check connectivity to all six specialized databases.\"\n\nI'm here to help with trading and market analysis. I can assist with:\n\n📊 **Market Analysis**: Stock quotes, technical analysis, 6-point breakdowns\n📈 **Trading Strategies**: Position sizing, risk management, entry/exit plans\n🧮 **Calculations**: VaR, portfolio optimization, risk metrics\n📚 **Education**: Trading concepts, market terminology, strategy explanations\n\nFeel free to ask about any specific stocks, trading concepts, or analysis you'd like help with!\n            ", "response_time": 0.0013616085052490234, "status_code": 200}}]}