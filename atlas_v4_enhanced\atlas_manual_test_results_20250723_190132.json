{"timestamp": "2025-07-23T19:01:32.057903", "total_tests": 34, "successful": 34, "failed": 0, "errors": 0, "success_rate": 100.0, "results": [{"category": "Category A - Core AI & Conversational Features", "question": "What's the difference between a market order and a limit order?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'What's the difference between a market order and a limit order?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 2.047285318374634, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "Explain put‑call parity in simple terms.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Explain put‑call parity in simple terms.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.001546621322631836, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "Give me a 6‑Point analysis for TSLA today.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Give me a 6‑Point analysis for TSLA today.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0014085769653320312, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "What's your market view on AAPL?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'What's your market view on AAPL?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0014472007751464844, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "Tell me about AAPL trend", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Tell me about AAPL trend'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012788772583007812, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "What if I wanted a tighter stop?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'What if I wanted a tighter stop?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012388229370117188, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "Give me a beginner‑level explanation of RSI", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Give me a beginner‑level explanation of RSI'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.00138092041015625, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "Now explain it like I'm an expert", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Now explain it like I'm an expert'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012404918670654297, "status_code": 200}}, {"category": "Category A - Core AI & Conversational Features", "question": "I'm freaked out by volatility. How do I manage risk?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'I'm freaked out by volatility. How do I manage risk?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012505054473876953, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "Scan MSFT on daily for the Lee Method signal—what do you see?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Scan MSFT on daily for the Lee Method signal—what do you see?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0019381046295166016, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "What does the current TTM Squeeze histogram show for NVDA at 15 min?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'What does the current TTM Squeeze histogram show for NVDA at 15 min?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0014073848724365234, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "List the top 5 S&P 500 stocks with Lee Method buy signals right now.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'List the top 5 S&P 500 stocks with Lee Method buy signals right now.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0013294219970703125, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "Provide a 6‑Point trade plan for GOOGL at market open.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Provide a 6‑Point trade plan for GOOGL at market open.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012636184692382812, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "Construct an Iron Condor on SPY expiring in 30 days targeting $500 profit, moderate risk.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Construct an Iron Condor on SPY expiring in 30 days targeting $500 profit, moderate risk.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012447834014892578, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "Optimize a portfolio of AAPL, TSLA, NVDA for 10% annualized return.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Optimize a portfolio of AAPL, TSLA, NVDA for 10% annualized return.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0014085769653320312, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "Calculate my portfolio's 95% VaR.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Calculate my portfolio's 95% VaR.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0013196468353271484, "status_code": 200}}, {"category": "Category B - Trading & Analysis Features", "question": "Which market regime are we in—bull, bear, or sideways?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Which market regime are we in—bull, bear, or sideways?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0014598369598388672, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "What's AMZN trading at right now?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'What's AMZN trading at right now?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0013244152069091797, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "What's TSLA's last price?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'What's TSLA's last price?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.001363992691040039, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "Summarize the latest headlines impacting AAPL.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Summarize the latest headlines impacting AAPL.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012879371643066406, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "What's the Twitter sentiment on GME today?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'What's the Twitter sentiment on GME today?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.001383066177368164, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "Forecast MSFT's next 5 days using LSTM.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Forecast MSFT's next 5 days using LSTM.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0013644695281982422, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "Use satellite oil‐rig data to gauge energy sector outlook.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Use satellite oil‐rig data to gauge energy sector outlook.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012738704681396484, "status_code": 200}}, {"category": "Category C - Market Data & Intelligence", "question": "Get live quotes for 7203.T (Toyota) on TSE.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Get live quotes for 7203.T (Toyota) on TSE.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012786388397216797, "status_code": 200}}, {"category": "Category D - Real-time Capabilities", "question": "Subscribe to TSLA scanner alerts—did you get a sub‑second notification?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Subscribe to TSLA scanner alerts—did you get a sub‑second notification?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.001257181167602539, "status_code": 200}}, {"category": "Category D - Real-time Capabilities", "question": "Scan 500 symbols and verify results return within 5 seconds.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Scan 500 symbols and verify results return within 5 seconds.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012657642364501953, "status_code": 200}}, {"category": "Category D - Real-time Capabilities", "question": "Show real‑time P&L updates for my open positions.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Show real‑time P&L updates for my open positions.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0013709068298339844, "status_code": 200}}, {"category": "Category D - Real-time Capabilities", "question": "What's the scanner progress after 30 seconds?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'What's the scanner progress after 30 seconds?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0013706684112548828, "status_code": 200}}, {"category": "Category D - Real-time Capabilities", "question": "What's the weather?", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'What's the weather?'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0013713836669921875, "status_code": 200}}, {"category": "Category E - Advanced Features", "question": "Send me the 8:00 AM CT market briefing.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Send me the 8:00 AM CT market briefing.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012290477752685547, "status_code": 200}}, {"category": "Category E - Advanced Features", "question": "Retrieve Chapter 3 summary from the TTM Squeeze book.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Retrieve Chapter 3 summary from the TTM Squeeze book.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012385845184326172, "status_code": 200}}, {"category": "Category E - Advanced Features", "question": "Place a paper trade: buy 100 AAPL at market.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Place a paper trade: buy 100 AAPL at market.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012447834014892578, "status_code": 200}}, {"category": "Category E - Advanced Features", "question": "Report current CPU, memory, and API‐latency metrics.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Report current CPU, memory, and API‐latency metrics.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012369155883789062, "status_code": 200}}, {"category": "Category E - Advanced Features", "question": "Check connectivity to all six specialized databases.", "result": {"status": "SUCCESS", "response": "[TESTING MODE] Received your message: 'Check connectivity to all six specialized databases.'. This is a simulated response for testing the Atlas V5 Enhanced system interface.", "response_time": 0.0012328624725341797, "status_code": 200}}]}