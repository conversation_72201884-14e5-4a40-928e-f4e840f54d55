{"test_run_info": {"timestamp": "2025-07-23T19:12:26.282387", "total_time": 0.14808988571166992, "base_url": "http://localhost:8002"}, "overall_results": {"total_tests": 34, "passed": 29, "failed": 4, "errors": 0, "skipped": 1, "pass_rate": 85.29, "avg_response_time": 0.004}, "category_breakdown": {"Category A": {"total": 8, "passed": 7, "failed": 1, "errors": 0, "skipped": 0}, "Category B": {"total": 8, "passed": 8, "failed": 0, "errors": 0, "skipped": 0}, "Category C": {"total": 7, "passed": 5, "failed": 2, "errors": 0, "skipped": 0}, "Category D": {"total": 5, "passed": 4, "failed": 0, "errors": 0, "skipped": 1}, "Category E": {"total": 2, "passed": 2, "failed": 0, "errors": 0, "skipped": 0}, "Category F": {"total": 2, "passed": 2, "failed": 0, "errors": 0, "skipped": 0}, "Category G": {"total": 1, "passed": 0, "failed": 1, "errors": 0, "skipped": 0}, "Category H": {"total": 1, "passed": 1, "failed": 0, "errors": 0, "skipped": 0}}, "detailed_results": [{"category": "Category A", "test_name": "Conversational AI", "description": "What's the difference between a market order and a limit order?", "status": "PASS", "response_time": 0.0012691020965576172, "details": {"response_length": 522}, "error_message": null, "timestamp": "2025-07-23T19:12:26.135647"}, {"category": "Category A", "test_name": "Conversational AI", "description": "Explain put-call parity in simple terms.", "status": "PASS", "response_time": 0.0009205341339111328, "details": {"response_length": 154}, "error_message": null, "timestamp": "2025-07-23T19:12:26.136717"}, {"category": "Category A", "test_name": "6-Point Analysis", "description": "Analysis for TSLA", "status": "PASS", "response_time": 0.0008614063262939453, "details": {"has_six_points": true, "response_length": 899}, "error_message": null, "timestamp": "2025-07-23T19:12:26.137683"}, {"category": "Category A", "test_name": "Grok Integration", "description": "Normal Grok request", "status": "PASS", "response_time": 0.0008029937744140625, "details": {"grok_used": false}, "error_message": null, "timestamp": "2025-07-23T19:12:26.138580"}, {"category": "Category A", "test_name": "Grok Fallback", "description": "Fallback to OpenAI/static", "status": "PASS", "response_time": 0.0009515285491943359, "details": {"fallback_used": false}, "error_message": null, "timestamp": "2025-07-23T19:12:26.140247"}, {"category": "Category A", "test_name": "Context Awareness", "description": "Multi-turn conversation", "status": "FAIL", "response_time": 0.0016503334045410156, "details": {"remembers_context": false}, "error_message": null, "timestamp": "2025-07-23T19:12:26.142018"}, {"category": "Category A", "test_name": "Adaptive Communication", "description": "<PERSON><PERSON><PERSON> vs Expert explanation", "status": "PASS", "response_time": 0.0016589164733886719, "details": {"beginner_length": 537, "expert_length": 544, "adapts_level": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.143789"}, {"category": "Category A", "test_name": "Emotional Intelligence", "description": "Emotion detection and response", "status": "PASS", "response_time": 0.0007882118225097656, "details": {"shows_empathy": true, "emotion_detected": null}, "error_message": null, "timestamp": "2025-07-23T19:12:26.144694"}, {"category": "Category B", "test_name": "Lee Method Detection", "description": "MSFT daily scan", "status": "PASS", "response_time": 0.0008122920989990234, "details": {"has_lee_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.146002"}, {"category": "Category B", "test_name": "TTM Squeeze Detection", "description": "NVDA 15min histogram", "status": "PASS", "response_time": 0.0009479522705078125, "details": {"has_ttm_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.147725"}, {"category": "Category B", "test_name": "Real-time Scanner", "description": "Top 5 Lee Method signals", "status": "PASS", "response_time": 0.0008339881896972656, "details": {"symbols_found": 9}, "error_message": null, "timestamp": "2025-07-23T19:12:26.148775"}, {"category": "Category B", "test_name": "Trading Plan Generation", "description": "6-Point plan for GOOGL", "status": "PASS", "response_time": 0.0007584095001220703, "details": {"has_plan_elements": true, "numbered_points": 6}, "error_message": null, "timestamp": "2025-07-23T19:12:26.150360"}, {"category": "Category B", "test_name": "Options Trading", "description": "Iron Condor construction", "status": "PASS", "response_time": 0.001459360122680664, "details": {"has_options_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.152148"}, {"category": "Category B", "test_name": "Portfolio Optimization", "description": "3-stock portfolio optimization", "status": "PASS", "response_time": 0.0013535022735595703, "details": {"has_portfolio_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.154095"}, {"category": "Category B", "test_name": "Risk Management", "description": "95% VaR calculation", "status": "PASS", "response_time": 0.001397848129272461, "details": {"has_var_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.155635"}, {"category": "Category B", "test_name": "Market Regime", "description": "Current regime identification", "status": "PASS", "response_time": 0.0017352104187011719, "details": {"has_regime_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.157640"}, {"category": "Category C", "test_name": "Real-time Quotes", "description": "AMZN current price", "status": "FAIL", "response_time": 0.0010519027709960938, "details": {"has_price": false}, "error_message": null, "timestamp": "2025-07-23T19:12:26.158939"}, {"category": "Category C", "test_name": "Data Fallback", "description": "TSLA fallback price", "status": "FAIL", "response_time": 0.001043558120727539, "details": {"has_fallback_price": false, "fallback_used": false}, "error_message": null, "timestamp": "2025-07-23T19:12:26.160084"}, {"category": "Category C", "test_name": "News Integration", "description": "AAPL news summary", "status": "PASS", "response_time": 0.0008795261383056641, "details": {"has_news_content": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.161268"}, {"category": "Category C", "test_name": "Sentiment Analysis", "description": "GME Twitter sentiment", "status": "PASS", "response_time": 0.0009989738464355469, "details": {"has_sentiment_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.162365"}, {"category": "Category C", "test_name": "ML Predictions", "description": "MSFT 5-day LSTM forecast", "status": "PASS", "response_time": 0.0008544921875, "details": {"has_ml_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.163365"}, {"category": "Category C", "test_name": "Alternative Data", "description": "Satellite oil-rig analysis", "status": "PASS", "response_time": 0.0008289813995361328, "details": {"has_alt_data_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.164374"}, {"category": "Category C", "test_name": "Global Markets", "description": "7203.T TSE quote", "status": "PASS", "response_time": 0.0007817745208740234, "details": {"has_global_analysis": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.165264"}, {"category": "Category D", "test_name": "WebSocket Alerts", "description": "TSLA scanner alerts", "status": "SKIP", "response_time": 0.0, "details": {"reason": "WebSocket testing requires specialized setup"}, "error_message": null, "timestamp": "2025-07-23T19:12:26.165570"}, {"category": "Category D", "test_name": "Scanner Performance", "description": "500 symbols < 5 seconds", "status": "PASS", "response_time": 0.10605025291442871, "details": {"scan_time": 0.104752, "meets_performance": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.271721"}, {"category": "Category D", "test_name": "Live P&L Monitoring", "description": "Real-time P&L updates", "status": "PASS", "response_time": 0.0010447502136230469, "details": {"has_pnl_data": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.272957"}, {"category": "Category D", "test_name": "Progress Tracking", "description": "Scanner progress after 30 seconds", "status": "PASS", "response_time": 0.0008916854858398438, "details": {"has_progress_data": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.274034"}, {"category": "Category D", "test_name": "Conversation Monitoring", "description": "Off-topic refocus", "status": "PASS", "response_time": 0.0008623600006103516, "details": {"refocuses_on_trading": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.275019"}, {"category": "Category E", "test_name": "Morning Briefing", "description": "8:00 AM CT briefing", "status": "PASS", "response_time": 0.0008845329284667969, "details": {"has_briefing": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.276155"}, {"category": "Category E", "test_name": "Educational RAG", "description": "TTM Squeeze book retrieval", "status": "PASS", "response_time": 0.0009558200836181641, "details": {"has_content": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.277215"}, {"category": "Category F", "test_name": "Health Endpoint", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.000820159912109375, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-23T19:12:26.278237"}, {"category": "Category F", "test_name": "Trading API", "description": "GET /api/v1/trading/positions", "status": "PASS", "response_time": 0.0007882118225097656, "details": {"has_positions": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.279106"}, {"category": "Category G", "test_name": "Multi-level Caching", "description": "Quote caching verification", "status": "FAIL", "response_time": 0.0019180774688720703, "details": {"first_request_time": 0.0007994174957275391, "second_request_time": 0.0011186599731445312, "cache_working": false}, "error_message": null, "timestamp": "2025-07-23T19:12:26.281205"}, {"category": "Category H", "test_name": "Division by Zero Protection", "description": "Position sizing edge case", "status": "PASS", "response_time": 0.00087738037109375, "details": {"handles_error": true}, "error_message": null, "timestamp": "2025-07-23T19:12:26.282278"}], "production_readiness": {"ready": true, "critical_failures": [], "recommendations": ["Core AI functionality issues detected. Verify Grok integration and fallback mechanisms."]}}